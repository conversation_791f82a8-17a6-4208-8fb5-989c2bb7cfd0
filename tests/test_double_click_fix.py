"""测试托盘双击功能修复。

验证修复后的双击检测逻辑是否正常工作。
"""
import sys
import time
import threading
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

try:
    import pystray
    from pystray import MenuItem as item
    from PIL import Image
    PYSTRAY_AVAILABLE = True
except ImportError:
    PYSTRAY_AVAILABLE = False

import tkinter as tk
from tkinter import messagebox


class DoubleClickTestApp:
    """双击测试应用"""
    
    def __init__(self):
        self.root = None
        self.tray_icon = None
        self.tray_thread = None
        self.is_tray_running = False
        
        # 双击检测
        self.last_click_time = 0
        self.double_click_threshold = 0.5
        self.click_count = 0
        self.double_click_count = 0
        
    def create_window(self):
        """创建测试窗口"""
        self.root = tk.Tk()
        self.root.title("托盘双击功能测试")
        self.root.geometry("500x400")
        
        # 标题
        title_label = tk.Label(
            self.root,
            text="托盘双击功能测试",
            font=("Arial", 16, "bold")
        )
        title_label.pack(pady=20)
        
        # 说明
        info_text = """测试说明：
1. 点击"最小化到托盘"按钮
2. 单击托盘图标 - 应该没有反应
3. 双击托盘图标 - 应该恢复窗口
4. 观察下方的统计信息

注意：双击需要在500毫秒内完成两次点击"""
        
        info_label = tk.Label(
            self.root,
            text=info_text,
            justify="left",
            font=("Arial", 10)
        )
        info_label.pack(pady=10)
        
        # 最小化按钮
        minimize_btn = tk.Button(
            self.root,
            text="最小化到托盘",
            command=self.minimize_to_tray,
            bg="lightblue",
            font=("Arial", 12)
        )
        minimize_btn.pack(pady=10)
        
        # 统计信息
        self.stats_label = tk.Label(
            self.root,
            text="点击统计：单击 0 次，双击 0 次",
            fg="blue",
            font=("Arial", 11)
        )
        self.stats_label.pack(pady=10)
        
        # 状态标签
        self.status_label = tk.Label(
            self.root,
            text="状态：程序运行中",
            fg="green",
            font=("Arial", 11)
        )
        self.status_label.pack(pady=20)
        
        # 重置统计按钮
        reset_btn = tk.Button(
            self.root,
            text="重置统计",
            command=self.reset_stats,
            bg="lightgray"
        )
        reset_btn.pack(pady=5)
        
        # 设置关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def reset_stats(self):
        """重置统计信息"""
        self.click_count = 0
        self.double_click_count = 0
        self.update_stats()
        
    def update_stats(self):
        """更新统计显示"""
        self.stats_label.config(
            text=f"点击统计：单击 {self.click_count} 次，双击 {self.double_click_count} 次"
        )
        
    def create_tray_icon(self):
        """创建托盘图标"""
        if not PYSTRAY_AVAILABLE:
            messagebox.showerror("错误", "pystray 不可用，请安装：pip install pystray pillow")
            return False
            
        try:
            # 创建简单图标
            try:
                icon_path = project_root / "resources" / "22.png"
                if icon_path.exists():
                    icon_image = Image.open(str(icon_path))
                else:
                    icon_image = Image.new('RGB', (64, 64), color='red')
            except:
                icon_image = Image.new('RGB', (64, 64), color='red')
            
            # 创建菜单
            menu = pystray.Menu(
                item('显示主界面', self.on_restore_clicked, default=True),
                pystray.Menu.SEPARATOR,
                item('重置统计', self.reset_stats_from_tray),
                item('退出程序', self.on_quit_clicked)
            )
            
            # 创建托盘图标
            self.tray_icon = pystray.Icon(
                "双击测试",
                icon_image,
                menu=menu
            )
            
            # 设置双击检测事件
            def on_activate(icon, item):
                """处理点击事件，区分单击和双击"""
                current_time = time.time()
                time_since_last_click = current_time - self.last_click_time
                
                print(f"托盘图标被点击，距离上次点击: {time_since_last_click:.3f}秒")
                
                # 如果距离上次点击时间小于阈值，认为是双击
                if time_since_last_click < self.double_click_threshold:
                    print("检测到双击！")
                    self.double_click_count += 1
                    self.last_click_time = 0  # 重置，避免三击
                    
                    # 恢复窗口
                    self.restore_from_tray()
                    
                else:
                    print("检测到单击，等待可能的双击...")
                    self.click_count += 1
                    self.last_click_time = current_time
                
                # 更新统计（在主线程中）
                if self.root:
                    self.root.after(0, self.update_stats)
            
            self.tray_icon.default_action = on_activate
            
            print("托盘图标创建成功")
            return True
            
        except Exception as e:
            print(f"创建托盘图标失败: {e}")
            messagebox.showerror("错误", f"创建托盘图标失败: {e}")
            return False
    
    def show_tray_icon(self):
        """显示托盘图标"""
        if not self.tray_icon or self.is_tray_running:
            return False
            
        try:
            self.tray_thread = threading.Thread(
                target=self._run_tray_icon,
                daemon=True
            )
            self.tray_thread.start()
            self.is_tray_running = True
            print("托盘图标已显示")
            return True
        except Exception as e:
            print(f"显示托盘图标失败: {e}")
            return False
    
    def _run_tray_icon(self):
        """运行托盘图标"""
        try:
            if self.tray_icon:
                self.tray_icon.run()
        except Exception as e:
            print(f"托盘图标运行错误: {e}")
    
    def minimize_to_tray(self):
        """最小化到托盘"""
        print("正在最小化到托盘...")
        
        if not self.is_tray_running:
            if not self.create_tray_icon():
                return False
            if not self.show_tray_icon():
                return False
        
        # 隐藏窗口
        self.root.withdraw()
        self.status_label.config(text="状态：已最小化到托盘，请双击托盘图标恢复")
        print("窗口已隐藏到托盘")
        return True
    
    def restore_from_tray(self):
        """从托盘恢复窗口"""
        def do_restore():
            try:
                print("正在恢复窗口...")
                
                if self.root:
                    # 恢复窗口
                    current_state = self.root.state()
                    print(f"当前窗口状态: {current_state}")
                    
                    if current_state == 'withdrawn':
                        self.root.deiconify()
                    elif current_state == 'iconic':
                        self.root.deiconify()
                    
                    self.root.state('normal')
                    self.root.lift()
                    self.root.focus_force()
                    
                    # 临时置顶
                    self.root.attributes('-topmost', True)
                    self.root.after(200, lambda: self.root.attributes('-topmost', False))
                    
                    # 更新状态
                    self.status_label.config(
                        text="状态：窗口已通过双击恢复",
                        fg="green"
                    )
                    
                    print("窗口恢复成功")
                    
            except Exception as e:
                print(f"恢复窗口失败: {e}")
                if self.root:
                    self.status_label.config(
                        text=f"状态：恢复失败 - {e}",
                        fg="red"
                    )
        
        # 使用after方法确保在主线程中执行
        if self.root:
            self.root.after(0, do_restore)
        else:
            do_restore()
    
    def on_restore_clicked(self, icon=None, item=None):
        """托盘菜单恢复点击"""
        print("托盘菜单'显示主界面'被点击")
        self.restore_from_tray()
    
    def reset_stats_from_tray(self, icon=None, item=None):
        """从托盘菜单重置统计"""
        print("从托盘菜单重置统计")
        if self.root:
            self.root.after(0, self.reset_stats)
    
    def on_quit_clicked(self, icon=None, item=None):
        """托盘菜单退出点击"""
        print("托盘菜单'退出程序'被点击")
        self.cleanup()
        if self.root:
            self.root.after(0, self.root.quit)
    
    def on_closing(self):
        """窗口关闭事件"""
        print("窗口关闭事件")
        self.cleanup()
        self.root.quit()
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.tray_icon and self.is_tray_running:
                self.tray_icon.stop()
                self.is_tray_running = False
            print("资源清理完成")
        except Exception as e:
            print(f"清理资源时出错: {e}")
    
    def run(self):
        """运行测试"""
        if not PYSTRAY_AVAILABLE:
            messagebox.showerror("错误", "pystray 不可用，请安装：pip install pystray pillow")
            return
            
        self.create_window()
        print("托盘双击功能测试启动")
        print("请按照界面说明进行测试...")
        self.root.mainloop()


def main():
    """主函数"""
    print("=" * 60)
    print("托盘双击功能修复测试")
    print("=" * 60)
    print("此测试验证修复后的双击检测逻辑")
    print("单击托盘图标应该没有反应")
    print("双击托盘图标应该恢复窗口")
    print("=" * 60)
    
    test = DoubleClickTestApp()
    test.run()
    
    print("测试结束")


if __name__ == "__main__":
    main()