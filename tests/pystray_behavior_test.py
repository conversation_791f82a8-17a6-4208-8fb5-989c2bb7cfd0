"""测试pystray在Windows上的行为。

了解pystray在Windows上如何处理不同的鼠标事件。
"""
import sys
import time
import threading
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

try:
    import pystray
    from pystray import MenuItem as item
    from PIL import Image
    PYSTRAY_AVAILABLE = True
except ImportError:
    PYSTRAY_AVAILABLE = False

import tkinter as tk


class PystrayBehaviorTest:
    """pystray行为测试"""
    
    def __init__(self):
        self.root = None
        self.tray_icon = None
        self.tray_thread = None
        self.is_tray_running = False
        self.event_count = 0
        
    def create_window(self):
        """创建测试窗口"""
        self.root = tk.Tk()
        self.root.title("Pystray行为测试")
        self.root.geometry("600x400")
        
        # 标题
        title_label = tk.Label(
            self.root,
            text="Pystray在Windows上的行为测试",
            font=("Arial", 16, "bold")
        )
        title_label.pack(pady=10)
        
        # 说明
        info_text = """测试说明：
1. 点击"创建托盘图标"
2. 尝试不同的鼠标操作：
   - 左键单击托盘图标
   - 左键双击托盘图标
   - 右键单击托盘图标（显示菜单）
3. 观察下方的事件日志

目标：了解pystray如何处理这些事件"""
        
        info_label = tk.Label(
            self.root,
            text=info_text,
            justify="left",
            font=("Arial", 10)
        )
        info_label.pack(pady=10)
        
        # 创建托盘按钮
        create_btn = tk.Button(
            self.root,
            text="创建托盘图标",
            command=self.create_and_show_tray,
            bg="lightgreen",
            font=("Arial", 12)
        )
        create_btn.pack(pady=10)
        
        # 清除日志按钮
        clear_btn = tk.Button(
            self.root,
            text="清除日志",
            command=self.clear_log,
            bg="lightgray"
        )
        clear_btn.pack(pady=5)
        
        # 事件日志
        log_label = tk.Label(
            self.root,
            text="事件日志：",
            font=("Arial", 12, "bold")
        )
        log_label.pack(pady=(20, 5))
        
        # 日志文本框
        self.log_text = tk.Text(
            self.root,
            height=12,
            width=70,
            font=("Consolas", 9)
        )
        self.log_text.pack(pady=5, padx=10, fill="both", expand=True)
        
        # 滚动条
        scrollbar = tk.Scrollbar(self.log_text)
        scrollbar.pack(side="right", fill="y")
        self.log_text.config(yscrollcommand=scrollbar.set)
        scrollbar.config(command=self.log_text.yview)
        
        # 设置关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def log_event(self, message):
        """记录事件到日志"""
        import datetime
        now = datetime.datetime.now()
        timestamp = now.strftime("%H:%M:%S.%f")[:-3]  # 精确到毫秒
        self.event_count += 1
        log_message = f"[{timestamp}] #{self.event_count}: {message}\n"
        
        # 添加到文本框
        self.log_text.insert("end", log_message)
        self.log_text.see("end")  # 滚动到底部
        
        # 同时打印到控制台
        print(log_message.strip())
        
    def clear_log(self):
        """清除日志"""
        self.log_text.delete("1.0", "end")
        self.event_count = 0
        self.log_event("日志已清除")
        
    def create_tray_icon(self):
        """创建托盘图标"""
        if not PYSTRAY_AVAILABLE:
            self.log_event("错误：pystray 不可用")
            return False
            
        try:
            # 创建简单图标
            icon_image = Image.new('RGB', (64, 64), color='purple')
            
            # 创建菜单
            menu = pystray.Menu(
                item('显示测试窗口', self.on_menu_show, default=True),
                item('菜单项2', self.on_menu_item2),
                pystray.Menu.SEPARATOR,
                item('退出程序', self.on_menu_quit)
            )
            
            # 创建托盘图标
            self.tray_icon = pystray.Icon(
                "行为测试",
                icon_image,
                menu=menu
            )
            
            # 测试不同的事件处理方式
            
            # 方法1：default_action
            def on_default_action(icon, item):
                """default_action处理函数"""
                self.log_event("default_action 被触发")
                self.restore_window()
            
            self.tray_icon.default_action = on_default_action
            
            self.log_event("托盘图标创建成功")
            self.log_event("已设置 default_action 处理函数")
            return True
            
        except Exception as e:
            self.log_event(f"创建托盘图标失败: {e}")
            return False
    
    def show_tray_icon(self):
        """显示托盘图标"""
        if not self.tray_icon or self.is_tray_running:
            return False
            
        try:
            self.tray_thread = threading.Thread(
                target=self._run_tray_icon,
                daemon=True
            )
            self.tray_thread.start()
            self.is_tray_running = True
            self.log_event("托盘图标已显示")
            return True
        except Exception as e:
            self.log_event(f"显示托盘图标失败: {e}")
            return False
    
    def _run_tray_icon(self):
        """运行托盘图标"""
        try:
            if self.tray_icon:
                self.tray_icon.run()
        except Exception as e:
            self.log_event(f"托盘图标运行错误: {e}")
    
    def create_and_show_tray(self):
        """创建并显示托盘图标"""
        if self.is_tray_running:
            self.log_event("托盘图标已在运行")
            return
            
        if self.create_tray_icon():
            if self.show_tray_icon():
                self.log_event("托盘图标创建并显示成功")
                self.log_event("请尝试不同的鼠标操作：")
                self.log_event("- 左键单击托盘图标")
                self.log_event("- 左键双击托盘图标")
                self.log_event("- 右键单击托盘图标")
            else:
                self.log_event("显示托盘图标失败")
        else:
            self.log_event("创建托盘图标失败")
    
    def restore_window(self):
        """恢复窗口"""
        def do_restore():
            try:
                self.log_event("正在恢复窗口...")
                
                if self.root:
                    current_state = self.root.state()
                    self.log_event(f"当前窗口状态: {current_state}")
                    
                    if current_state == 'withdrawn':
                        self.root.deiconify()
                    elif current_state == 'iconic':
                        self.root.deiconify()
                    
                    self.root.state('normal')
                    self.root.lift()
                    self.root.focus_force()
                    
                    # 临时置顶
                    self.root.attributes('-topmost', True)
                    self.root.after(200, lambda: self.root.attributes('-topmost', False))
                    
                    self.log_event("窗口恢复成功")
                    
            except Exception as e:
                self.log_event(f"恢复窗口失败: {e}")
        
        # 使用after方法确保在主线程中执行
        if self.root:
            self.root.after(0, do_restore)
        else:
            do_restore()
    
    def on_menu_show(self, icon=None, item=None):
        """托盘菜单'显示测试窗口'点击"""
        self.log_event("托盘菜单'显示测试窗口'被点击")
        self.restore_window()
    
    def on_menu_item2(self, icon=None, item=None):
        """托盘菜单'菜单项2'点击"""
        self.log_event("托盘菜单'菜单项2'被点击")
    
    def on_menu_quit(self, icon=None, item=None):
        """托盘菜单'退出程序'点击"""
        self.log_event("托盘菜单'退出程序'被点击")
        self.cleanup()
        if self.root:
            self.root.after(0, self.root.quit)
    
    def on_closing(self):
        """窗口关闭事件"""
        self.log_event("窗口关闭事件")
        self.cleanup()
        self.root.quit()
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.tray_icon and self.is_tray_running:
                self.tray_icon.stop()
                self.is_tray_running = False
            self.log_event("资源清理完成")
        except Exception as e:
            self.log_event(f"清理资源时出错: {e}")
    
    def run(self):
        """运行测试"""
        if not PYSTRAY_AVAILABLE:
            print("错误：pystray 不可用，请安装：pip install pystray pillow")
            return
            
        self.create_window()
        self.log_event("Pystray行为测试启动")
        self.root.mainloop()


def main():
    """主函数"""
    print("=" * 60)
    print("Pystray在Windows上的行为测试")
    print("=" * 60)
    print("此工具用于了解pystray如何处理不同的鼠标事件")
    print("=" * 60)
    
    test = PystrayBehaviorTest()
    test.run()
    
    print("测试结束")


if __name__ == "__main__":
    main()