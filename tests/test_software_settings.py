"""软件设置功能测试脚本。

测试软件设置对话框和系统托盘功能。
"""
import os
import sys
import tempfile
from pathlib import Path

# 添加src路径
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

import ttkbootstrap as ttkb
from webhook_server.config import gui_constants
from webhook_server.gui.menu_dialogs import software_settings_dialog
from webhook_server.gui import system_tray
from webhook_server.models import gui_properties


def test_software_settings_dialog():
    """测试软件设置对话框"""
    print("测试软件设置对话框...")
    
    # 创建临时配置文件路径
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_config_path = os.path.join(temp_dir, "test_server_config.ini")
        
        # 创建基本的服务器配置文件
        with open(temp_config_path, 'w', encoding='utf-8') as f:
            f.write("""[server]
api_key=test_key
whitelist=*
message_data_table_name=test_messages
log_config_path=test_log.ini
host=127.0.0.1
port=8001
run_time=07:00-07:00
time_zone=Asia/Shanghai
expire_data_days=3
data_limit_num=100000
app_name=test_webhook_server
enable_sql_logging=True
""")
        
        # 创建测试窗口
        root = ttkb.Window(title="软件设置测试", size=(400, 300))
        
        try:
            # 创建GUI属性管理器
            props = gui_properties.GUIProperties(temp_config_path)
            props.root = root
            
            # 创建软件设置对话框组件
            settings_dialog = software_settings_dialog.SoftwareSettingsDialogComponent(props)
            
            # 测试配置加载
            print(f"默认最小化到托盘设置: {settings_dialog.get_minimize_to_tray_setting()}")
            
            # 创建测试按钮
            test_button = ttkb.Button(
                root,
                text="打开软件设置",
                command=settings_dialog.open_software_settings_dialog
            )
            test_button.pack(pady=20)
            
            # 创建系统托盘测试按钮
            tray_component = system_tray.SystemTrayComponent(props)
            if tray_component.is_available():
                tray_component.set_callbacks(
                    restore_callback=lambda: root.deiconify(),
                    quit_callback=lambda: root.quit()
                )
                
                tray_button = ttkb.Button(
                    root,
                    text="测试系统托盘",
                    command=lambda: tray_component.minimize_to_tray()
                )
                tray_button.pack(pady=10)
                
                restore_button = ttkb.Button(
                    root,
                    text="从托盘恢复",
                    command=lambda: tray_component.restore_from_tray()
                )
                restore_button.pack(pady=10)
            else:
                info_label = ttkb.Label(
                    root,
                    text="系统托盘功能不可用\n(需要安装 pystray 和 Pillow)",
                    foreground="red"
                )
                info_label.pack(pady=20)
            
            # 显示说明
            info_text = """
测试说明：
1. 点击"打开软件设置"测试设置对话框
2. 在设置中切换"最小化到任务栏"选项
3. 如果系统托盘可用，可以测试托盘功能
4. 关闭窗口查看是否按设置执行
"""
            info_label = ttkb.Label(root, text=info_text, justify="left")
            info_label.pack(pady=10)
            
            print("软件设置对话框测试窗口已创建")
            print("请手动测试各项功能...")
            
            # 运行测试窗口
            root.mainloop()
            
        except Exception as e:
            print(f"测试过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
        finally:
            try:
                root.destroy()
            except:
                pass


def test_config_file_operations():
    """测试配置文件操作"""
    print("\n测试配置文件操作...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # 临时修改软件配置路径
        original_path = gui_constants.SOFTWARE_CONFIG_PATH
        gui_constants.SOFTWARE_CONFIG_PATH = os.path.join(temp_dir, "software_config.ini")
        
        try:
            # 创建临时服务器配置
            temp_server_config = os.path.join(temp_dir, "server_config.ini")
            with open(temp_server_config, 'w', encoding='utf-8') as f:
                f.write("""[server]
api_key=test_key
whitelist=*
message_data_table_name=test_messages
log_config_path=test_log.ini
host=127.0.0.1
port=8002
run_time=07:00-07:00
time_zone=Asia/Shanghai
expire_data_days=3
data_limit_num=100000
app_name=test_webhook_server2
enable_sql_logging=True
""")
            
            # 创建GUI属性管理器
            props = gui_properties.GUIProperties(temp_server_config)
            
            # 创建软件设置组件
            settings = software_settings_dialog.SoftwareSettingsDialogComponent(props)
            
            # 测试默认配置
            print(f"初始设置 - 最小化到托盘: {settings.get_minimize_to_tray_setting()}")
            assert settings.get_minimize_to_tray_setting() == True, "默认应该是最小化到托盘"
            
            # 测试配置修改
            settings.software_config['minimize_to_tray'] = False
            settings.save_software_config()
            
            # 重新加载配置
            settings.load_software_config()
            print(f"修改后设置 - 最小化到托盘: {settings.get_minimize_to_tray_setting()}")
            assert settings.get_minimize_to_tray_setting() == False, "应该是直接关闭"
            
            # 验证配置文件存在
            assert os.path.exists(gui_constants.SOFTWARE_CONFIG_PATH), "配置文件应该被创建"
            
            print("✓ 配置文件操作测试通过")
            
        finally:
            # 恢复原始路径
            gui_constants.SOFTWARE_CONFIG_PATH = original_path


def main():
    """主测试函数"""
    print("开始软件设置功能测试...")
    print("=" * 50)
    
    try:
        # 测试配置文件操作
        test_config_file_operations()
        
        # 测试GUI对话框（需要用户交互）
        print("\n开始GUI测试（需要用户交互）...")
        test_software_settings_dialog()
        
        print("\n所有测试完成！")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
