"""调试系统托盘双击功能的脚本。

专门用于诊断双击事件不工作的问题。
"""
import sys
import threading
import time
from pathlib import Path

# 添加src路径
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

try:
    import pystray
    from pystray import MenuItem as item
    from PIL import Image
    PYSTRAY_AVAILABLE = True
    # pystray 可能没有 __version__ 属性
    version = getattr(pystray, '__version__', 'unknown')
    print(f"✓ pystray 已导入 (版本: {version})")
except ImportError as e:
    PYSTRAY_AVAILABLE = False
    print(f"✗ pystray 不可用: {e}")

import ttkbootstrap as ttkb
from webhook_server.config import gui_constants


def debug_double_click_test():
    """调试双击测试"""
    if not PYSTRAY_AVAILABLE:
        print("请先安装依赖: pip install pystray pillow")
        return
    
    print("=" * 60)
    print("开始调试系统托盘双击功能")
    print("=" * 60)
    
    # 创建测试窗口
    root = ttkb.Window(title="双击调试测试", size=(500, 400))
    
    # 全局变量
    tray_icon = None
    click_count = 0
    double_click_count = 0
    
    def log_message(msg):
        """记录消息"""
        timestamp = time.strftime("%H:%M:%S")
        print(f"[{timestamp}] {msg}")
    
    def restore_window():
        """恢复窗口"""
        nonlocal double_click_count
        double_click_count += 1
        log_message(f"恢复窗口被调用 (第{double_click_count}次)")
        
        try:
            if root.winfo_exists():
                root.deiconify()
                root.lift()
                root.focus_force()
                root.attributes('-topmost', True)
                root.after(100, lambda: root.attributes('-topmost', False))
                log_message("✓ 窗口恢复成功")
                
                # 更新界面显示
                status_label.config(text=f"状态: 窗口已恢复 (双击次数: {double_click_count})")
            else:
                log_message("✗ 窗口不存在")
        except Exception as e:
            log_message(f"✗ 恢复窗口失败: {e}")
    
    def hide_to_tray():
        """隐藏到托盘"""
        nonlocal tray_icon
        log_message("开始隐藏到托盘...")
        
        try:
            if not tray_icon:
                log_message("创建托盘图标...")
                
                # 加载图标
                icon_image = Image.open(gui_constants.SMALL_ICON_PATH)
                log_message(f"✓ 图标加载成功: {gui_constants.SMALL_ICON_PATH}")
                
                # 定义事件处理函数
                def on_left_click(icon):
                    """左键单击事件"""
                    nonlocal click_count
                    click_count += 1
                    log_message(f"左键单击 (第{click_count}次)")
                
                def on_double_click(icon, item):
                    """双击事件"""
                    log_message("双击事件触发!")
                    log_message(f"icon: {icon}, item: {item}")
                    
                    # 尝试多种方式调用恢复函数
                    try:
                        # 方式1: 直接调用
                        log_message("尝试方式1: 直接调用")
                        restore_window()
                    except Exception as e:
                        log_message(f"方式1失败: {e}")
                        
                        try:
                            # 方式2: 使用after方法
                            log_message("尝试方式2: 使用after方法")
                            root.after(0, restore_window)
                        except Exception as e2:
                            log_message(f"方式2也失败: {e2}")
                
                def on_menu_restore(icon, item):
                    """菜单恢复事件"""
                    log_message("菜单恢复被点击")
                    try:
                        root.after(0, restore_window)
                    except Exception as e:
                        log_message(f"菜单恢复失败: {e}")
                        restore_window()
                
                def on_quit(icon, item):
                    """退出事件"""
                    log_message("退出事件触发")
                    try:
                        icon.stop()
                        root.after(0, root.quit)
                    except Exception as e:
                        log_message(f"退出失败: {e}")
                
                # 创建菜单
                menu = pystray.Menu(
                    item('恢复窗口', on_menu_restore),
                    item('退出程序', on_quit)
                )
                
                # 创建托盘图标
                tray_icon = pystray.Icon(
                    "双击测试",
                    icon_image,
                    menu=menu
                )
                
                # 设置双击事件
                tray_icon.default_action = on_double_click
                log_message("✓ 双击事件已设置")
                
                # 在单独线程中运行
                def run_tray():
                    try:
                        log_message("托盘线程开始运行...")
                        tray_icon.run()
                        log_message("托盘线程结束")
                    except Exception as e:
                        log_message(f"托盘线程错误: {e}")
                
                tray_thread = threading.Thread(target=run_tray, daemon=True)
                tray_thread.start()
                log_message("✓ 托盘线程已启动")
                
                # 等待托盘图标准备就绪
                time.sleep(0.5)
            
            # 隐藏窗口
            root.withdraw()
            log_message("✓ 窗口已隐藏")
            log_message("请双击系统托盘图标测试...")
            
        except Exception as e:
            log_message(f"✗ 隐藏到托盘失败: {e}")
            import traceback
            traceback.print_exc()
    
    def quit_app():
        """退出应用"""
        log_message("退出应用...")
        try:
            if tray_icon:
                tray_icon.stop()
            root.quit()
        except Exception as e:
            log_message(f"退出时出错: {e}")
    
    # 创建界面
    title_label = ttkb.Label(
        root,
        text="系统托盘双击调试测试",
        font=("Arial", 16, "bold")
    )
    title_label.pack(pady=20)
    
    info_text = """
调试步骤：
1. 点击"隐藏到托盘"按钮
2. 查看控制台输出的调试信息
3. 双击系统托盘图标
4. 观察是否有双击事件日志
5. 检查窗口是否恢复显示

注意：请观察控制台的详细日志输出
"""
    info_label = ttkb.Label(
        root,
        text=info_text,
        justify="left",
        font=("Arial", 10)
    )
    info_label.pack(pady=10)
    
    # 状态显示
    status_label = ttkb.Label(
        root,
        text="状态: 准备测试",
        font=("Arial", 12),
        foreground="blue"
    )
    status_label.pack(pady=10)
    
    # 按钮
    hide_button = ttkb.Button(
        root,
        text="隐藏到托盘",
        bootstyle="primary",
        command=hide_to_tray
    )
    hide_button.pack(pady=10)
    
    restore_button = ttkb.Button(
        root,
        text="手动恢复窗口",
        bootstyle="secondary",
        command=restore_window
    )
    restore_button.pack(pady=5)
    
    quit_button = ttkb.Button(
        root,
        text="退出程序",
        bootstyle="danger",
        command=quit_app
    )
    quit_button.pack(pady=10)
    
    # 设置窗口关闭事件
    root.protocol("WM_DELETE_WINDOW", quit_app)
    
    log_message("调试测试窗口已创建")
    log_message("请按照界面说明进行测试，并观察控制台输出")
    
    try:
        root.mainloop()
    except KeyboardInterrupt:
        log_message("用户中断")
    finally:
        if tray_icon:
            try:
                tray_icon.stop()
            except:
                pass
        log_message("测试结束")


if __name__ == "__main__":
    debug_double_click_test()
