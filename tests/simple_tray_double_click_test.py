"""简化的托盘双击测试。

专门测试Windows系统上托盘双击功能的最小化实现。
"""
import sys
import time
import threading
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

try:
    import pystray
    from pystray import MenuItem as item
    from PIL import Image
    PYSTRAY_AVAILABLE = True
except ImportError:
    PYSTRAY_AVAILABLE = False

import tkinter as tk
from tkinter import messagebox


class SimpleTrayTest:
    """简化的托盘测试类"""
    
    def __init__(self):
        self.root = None
        self.tray_icon = None
        self.tray_thread = None
        self.is_tray_running = False
        self.restore_count = 0
        
    def create_window(self):
        """创建测试窗口"""
        self.root = tk.Tk()
        self.root.title("简化托盘双击测试")
        self.root.geometry("400x300")
        
        # 标题
        title_label = tk.Label(
            self.root,
            text="托盘双击测试",
            font=("Arial", 16, "bold")
        )
        title_label.pack(pady=20)
        
        # 说明
        info_label = tk.Label(
            self.root,
            text="1. 点击'最小化到托盘'按钮\n2. 双击托盘图标恢复窗口\n3. 右键托盘图标选择菜单",
            justify="left"
        )
        info_label.pack(pady=10)
        
        # 最小化按钮
        minimize_btn = tk.Button(
            self.root,
            text="最小化到托盘",
            command=self.minimize_to_tray,
            bg="lightblue"
        )
        minimize_btn.pack(pady=10)
        
        # 状态标签
        self.status_label = tk.Label(
            self.root,
            text="状态：程序运行中",
            fg="green"
        )
        self.status_label.pack(pady=20)
        
        # 设置关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def create_tray_icon(self):
        """创建托盘图标"""
        if not PYSTRAY_AVAILABLE:
            messagebox.showerror("错误", "pystray 不可用，请安装：pip install pystray pillow")
            return False
            
        try:
            # 创建一个简单的图标（如果没有图标文件）
            try:
                # 尝试使用项目图标
                icon_path = project_root / "resources" / "22.png"
                if icon_path.exists():
                    icon_image = Image.open(str(icon_path))
                else:
                    # 创建一个简单的图标
                    icon_image = Image.new('RGB', (64, 64), color='blue')
            except:
                # 创建一个简单的图标
                icon_image = Image.new('RGB', (64, 64), color='blue')
            
            # 创建菜单
            menu = pystray.Menu(
                item('显示主界面', self.on_restore_clicked, default=True),
                pystray.Menu.SEPARATOR,
                item('退出程序', self.on_quit_clicked)
            )
            
            # 创建托盘图标
            self.tray_icon = pystray.Icon(
                "托盘测试",
                icon_image,
                menu=menu
            )
            
            # 设置双击事件
            def on_activate(icon, item):
                """处理双击事件"""
                print("托盘图标被激活（双击或单击）")
                self.restore_from_tray()
            
            self.tray_icon.default_action = on_activate
            
            print("托盘图标创建成功")
            return True
            
        except Exception as e:
            print(f"创建托盘图标失败: {e}")
            messagebox.showerror("错误", f"创建托盘图标失败: {e}")
            return False
    
    def show_tray_icon(self):
        """显示托盘图标"""
        if not self.tray_icon or self.is_tray_running:
            return False
            
        try:
            self.tray_thread = threading.Thread(
                target=self._run_tray_icon,
                daemon=True
            )
            self.tray_thread.start()
            self.is_tray_running = True
            print("托盘图标已显示")
            return True
        except Exception as e:
            print(f"显示托盘图标失败: {e}")
            return False
    
    def _run_tray_icon(self):
        """运行托盘图标"""
        try:
            if self.tray_icon:
                self.tray_icon.run()
        except Exception as e:
            print(f"托盘图标运行错误: {e}")
    
    def minimize_to_tray(self):
        """最小化到托盘"""
        print("正在最小化到托盘...")
        
        if not self.is_tray_running:
            if not self.create_tray_icon():
                return False
            if not self.show_tray_icon():
                return False
        
        # 隐藏窗口
        self.root.withdraw()
        print("窗口已隐藏到托盘")
        return True
    
    def restore_from_tray(self):
        """从托盘恢复窗口"""
        def do_restore():
            try:
                self.restore_count += 1
                print(f"恢复窗口 (第{self.restore_count}次)")
                
                if self.root:
                    # 恢复窗口
                    current_state = self.root.state()
                    print(f"当前窗口状态: {current_state}")
                    
                    if current_state == 'withdrawn':
                        self.root.deiconify()
                    elif current_state == 'iconic':
                        self.root.deiconify()
                    
                    self.root.state('normal')
                    self.root.lift()
                    self.root.focus_force()
                    
                    # 临时置顶
                    self.root.attributes('-topmost', True)
                    self.root.after(200, lambda: self.root.attributes('-topmost', False))
                    
                    # 更新状态
                    self.status_label.config(
                        text=f"状态：窗口已恢复 (恢复次数: {self.restore_count})",
                        fg="green"
                    )
                    
                    print("窗口恢复成功")
                    
            except Exception as e:
                print(f"恢复窗口失败: {e}")
                if self.root:
                    self.status_label.config(
                        text=f"状态：恢复失败 - {e}",
                        fg="red"
                    )
        
        # 使用after方法确保在主线程中执行
        if self.root:
            self.root.after(0, do_restore)
        else:
            do_restore()
    
    def on_restore_clicked(self, icon=None, item=None):
        """托盘菜单恢复点击"""
        print("托盘菜单'显示主界面'被点击")
        self.restore_from_tray()
    
    def on_quit_clicked(self, icon=None, item=None):
        """托盘菜单退出点击"""
        print("托盘菜单'退出程序'被点击")
        self.cleanup()
        if self.root:
            self.root.after(0, self.root.quit)
    
    def on_closing(self):
        """窗口关闭事件"""
        print("窗口关闭事件")
        self.cleanup()
        self.root.quit()
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.tray_icon and self.is_tray_running:
                self.tray_icon.stop()
                self.is_tray_running = False
            print("资源清理完成")
        except Exception as e:
            print(f"清理资源时出错: {e}")
    
    def run(self):
        """运行测试"""
        if not PYSTRAY_AVAILABLE:
            messagebox.showerror("错误", "pystray 不可用，请安装：pip install pystray pillow")
            return
            
        self.create_window()
        print("简化托盘双击测试启动")
        print("请点击'最小化到托盘'按钮，然后双击托盘图标测试恢复功能")
        self.root.mainloop()


def main():
    """主函数"""
    print("=" * 50)
    print("简化托盘双击测试")
    print("=" * 50)
    
    test = SimpleTrayTest()
    test.run()
    
    print("测试结束")


if __name__ == "__main__":
    main()