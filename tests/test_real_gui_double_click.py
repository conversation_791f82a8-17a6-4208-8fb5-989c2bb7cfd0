"""测试实际GUI程序的双击功能。

使用真实的配置文件启动GUI程序，测试托盘双击功能。
"""
import os
import sys
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from webhook_server.gui import main_window


def create_test_config():
    """创建测试配置文件"""
    # 创建临时配置文件
    temp_dir = Path.home() / ".webhook_server_test"
    temp_dir.mkdir(exist_ok=True)
    
    import time
    config_path = temp_dir / f"test_config_{int(time.time())}.ini"
    
    # 写入基本配置
    config_content = f"""[server]
app_name = 托盘功能测试_{int(time.time())}
host = 0.0.0.0
port = 8002
log_config_path = {project_root / "resources" / "log.ini"}
time_zone = Asia/Shanghai
run_time = 07:00-07:00
expire_data_days = 3
data_limit_num = 100000
enable_sql_logging = true
message_data_table_name = test_msg_data
api_key = testkey123456789
whitelist = *

[client_info]
test_device = 测试设备
"""
    
    with open(config_path, 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    return str(config_path)


def main():
    """主测试函数"""
    print("=" * 60)
    print("实际GUI程序托盘双击功能测试")
    print("=" * 60)
    print("测试说明：")
    print("1. 程序启动后，进入'设置' -> '软件设置'")
    print("2. 确保勾选'点击右上角 X 时：最小化到任务栏'")
    print("3. 点击窗口右上角 X 按钮最小化到托盘")
    print("4. 单击托盘图标 - 应该没有反应")
    print("5. 双击托盘图标 - 应该恢复窗口")
    print("6. 右键托盘图标测试菜单功能")
    print("=" * 60)
    
    try:
        # 创建测试配置
        config_path = create_test_config()
        print(f"测试配置文件: {config_path}")
        
        # 启动GUI程序
        print("正在启动GUI程序...")
        main_app = main_window.WebhookServerGUI(config_path)
        
        # 显示测试提示
        import tkinter as tk
        from tkinter import messagebox
        
        def show_test_info():
            """显示测试信息"""
            messagebox.showinfo(
                "托盘双击测试",
                "测试说明：\n\n"
                "1. 进入'设置' -> '软件设置'\n"
                "2. 确保勾选'最小化到托盘'选项\n"
                "3. 点击窗口右上角 X 按钮\n"
                "4. 单击托盘图标 - 应该没有反应\n"
                "5. 双击托盘图标 - 应该恢复窗口\n\n"
                "注意：双击需要在500毫秒内完成"
            )
        
        # 延迟显示提示
        main_app.props.root.after(1000, show_test_info)
        
        # 启动主循环
        main_app.props.root.mainloop()
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试配置文件
        try:
            if 'config_path' in locals():
                os.remove(config_path)
                print(f"已清理测试配置文件: {config_path}")
        except:
            pass
    
    print("测试结束")


if __name__ == "__main__":
    main()