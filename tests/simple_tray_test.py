"""简单的系统托盘测试脚本。

用于验证pystray的基本功能。
"""
import sys
import threading
import time
from pathlib import Path

# 添加src路径
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

try:
    import pystray
    from pystray import MenuItem as item
    from PIL import Image
    PYSTRAY_AVAILABLE = True
except ImportError:
    PYSTRAY_AVAILABLE = False

import ttkbootstrap as ttkb
from webhook_server.config import gui_constants


def simple_tray_test():
    """简单的托盘测试"""
    if not PYSTRAY_AVAILABLE:
        print("pystray 不可用，请安装: pip install pystray pillow")
        return
    
    print("开始简单托盘测试...")
    
    # 创建测试窗口
    root = ttkb.Window(title="简单托盘测试", size=(400, 300))
    
    # 状态变量
    tray_icon = None
    is_hidden = False
    
    def show_window():
        """显示窗口"""
        nonlocal is_hidden
        print("显示窗口被调用")
        try:
            root.deiconify()
            root.lift()
            root.focus_force()
            root.attributes('-topmost', True)
            root.after(100, lambda: root.attributes('-topmost', False))
            is_hidden = False
            print("窗口已显示")
        except Exception as e:
            print(f"显示窗口时出错: {e}")
    
    def hide_to_tray():
        """隐藏到托盘"""
        nonlocal is_hidden, tray_icon
        print("隐藏到托盘...")
        try:
            if not tray_icon:
                # 创建托盘图标
                icon_image = Image.open(gui_constants.SMALL_ICON_PATH)
                
                def on_double_click(icon, item):
                    print("双击事件触发!")
                    # 在主线程中执行
                    root.after(0, show_window)
                
                def on_quit(icon, item):
                    print("退出事件触发!")
                    icon.stop()
                    root.after(0, root.quit)
                
                menu = pystray.Menu(
                    item('显示窗口', lambda icon, item: root.after(0, show_window)),
                    item('退出', on_quit)
                )
                
                tray_icon = pystray.Icon(
                    "简单测试",
                    icon_image,
                    menu=menu
                )
                
                # 设置双击事件
                tray_icon.default_action = on_double_click
                
                # 在单独线程中运行托盘
                def run_tray():
                    try:
                        tray_icon.run()
                    except Exception as e:
                        print(f"托盘运行错误: {e}")
                
                tray_thread = threading.Thread(target=run_tray, daemon=True)
                tray_thread.start()
                
                print("托盘图标已创建")
            
            # 隐藏窗口
            root.withdraw()
            is_hidden = True
            print("窗口已隐藏到托盘")
            
        except Exception as e:
            print(f"隐藏到托盘时出错: {e}")
    
    def quit_app():
        """退出应用"""
        nonlocal tray_icon
        print("退出应用...")
        try:
            if tray_icon:
                tray_icon.stop()
            root.quit()
        except Exception as e:
            print(f"退出时出错: {e}")
    
    # 创建界面
    title_label = ttkb.Label(
        root,
        text="简单托盘测试",
        font=("Arial", 16, "bold")
    )
    title_label.pack(pady=20)
    
    info_label = ttkb.Label(
        root,
        text="点击'隐藏到托盘'按钮，然后双击托盘图标测试",
        font=("Arial", 10)
    )
    info_label.pack(pady=10)
    
    hide_button = ttkb.Button(
        root,
        text="隐藏到托盘",
        bootstyle="primary",
        command=hide_to_tray
    )
    hide_button.pack(pady=10)
    
    show_button = ttkb.Button(
        root,
        text="显示窗口",
        bootstyle="secondary",
        command=show_window
    )
    show_button.pack(pady=5)
    
    quit_button = ttkb.Button(
        root,
        text="退出",
        bootstyle="danger",
        command=quit_app
    )
    quit_button.pack(pady=10)
    
    # 设置窗口关闭事件
    root.protocol("WM_DELETE_WINDOW", quit_app)
    
    print("测试窗口已创建，请测试托盘功能")
    
    try:
        root.mainloop()
    except KeyboardInterrupt:
        print("用户中断")
    finally:
        if tray_icon:
            try:
                tray_icon.stop()
            except:
                pass


if __name__ == "__main__":
    simple_tray_test()
