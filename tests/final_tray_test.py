"""最终的系统托盘功能测试。

测试修复后的双击/单击恢复功能。
"""
import os
import sys
import tempfile
from pathlib import Path

# 添加src路径
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

import ttkbootstrap as ttkb
from webhook_server.config import gui_constants
from webhook_server.gui.menu_dialogs import software_settings_dialog
from webhook_server.gui import system_tray
from webhook_server.models import gui_properties


def final_tray_test():
    """最终托盘功能测试"""
    print("=" * 60)
    print("最终系统托盘功能测试")
    print("=" * 60)
    
    # 创建临时配置文件
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_config_path = os.path.join(temp_dir, "test_server_config.ini")
        
        # 创建基本的服务器配置文件
        with open(temp_config_path, 'w', encoding='utf-8') as f:
            f.write("""[server]
api_key=test_key
whitelist=*
message_data_table_name=test_messages
log_config_path=test_log.ini
host=127.0.0.1
port=8004
run_time=07:00-07:00
time_zone=Asia/Shanghai
expire_data_days=3
data_limit_num=100000
app_name=test_webhook_server4
enable_sql_logging=True
""")
        
        # 创建测试窗口
        root = ttkb.Window(title="最终托盘测试", size=(600, 500))
        
        try:
            # 创建GUI属性管理器
            props = gui_properties.GUIProperties(temp_config_path)
            props.root = root
            
            # 创建软件设置组件
            settings_dialog = software_settings_dialog.SoftwareSettingsDialogComponent(props)
            
            # 创建系统托盘组件
            tray_component = system_tray.SystemTrayComponent(props)
            
            # 检查托盘功能是否可用
            if not tray_component.is_available():
                error_label = ttkb.Label(
                    root,
                    text="系统托盘功能不可用！\n请确保已安装 pystray 和 Pillow",
                    foreground="red",
                    font=("Arial", 14, "bold")
                )
                error_label.pack(pady=50)
                
                install_label = ttkb.Label(
                    root,
                    text="安装命令：pip install pystray pillow",
                    font=("Arial", 10),
                    foreground="blue"
                )
                install_label.pack(pady=10)
                
                close_button = ttkb.Button(
                    root,
                    text="关闭",
                    command=root.quit
                )
                close_button.pack(pady=20)
                
                root.mainloop()
                return
            
            # 恢复计数器
            restore_count = 0
            
            # 设置托盘回调函数
            def restore_callback():
                nonlocal restore_count
                restore_count += 1
                print(f"[恢复] 第 {restore_count} 次恢复窗口")
                
                try:
                    root.deiconify()
                    root.lift()
                    root.focus_force()
                    root.attributes('-topmost', True)
                    root.after(100, lambda: root.attributes('-topmost', False))
                    
                    # 更新状态显示
                    status_label.config(
                        text=f"状态：窗口已恢复 (恢复次数: {restore_count})",
                        foreground="green"
                    )
                    print(f"[恢复] 窗口恢复成功")
                    
                except Exception as e:
                    print(f"[错误] 恢复窗口失败: {e}")
                    status_label.config(
                        text=f"状态：恢复失败 - {e}",
                        foreground="red"
                    )
            
            def quit_callback():
                print("[退出] 退出程序")
                try:
                    tray_component.cleanup()
                    root.quit()
                except Exception as e:
                    print(f"[错误] 退出时出错: {e}")
            
            # 设置回调
            tray_component.set_callbacks(
                restore_callback=restore_callback,
                quit_callback=quit_callback
            )
            
            # 创建界面元素
            title_label = ttkb.Label(
                root,
                text="最终系统托盘功能测试",
                font=("Arial", 18, "bold"),
                foreground="blue"
            )
            title_label.pack(pady=20)
            
            # 功能说明
            info_frame = ttkb.Frame(root)
            info_frame.pack(pady=10, padx=20, fill=X)
            
            info_text = """
🔧 测试功能：
• 软件设置：配置点击X的行为（最小化/关闭）
• 系统托盘：最小化到托盘，双击/单击恢复
• 右键菜单：打开主界面、退出程序

📋 测试步骤：
1. 点击"软件设置"配置最小化行为
2. 点击"最小化到托盘"隐藏窗口
3. 双击托盘图标恢复窗口
4. 右键托盘图标使用菜单功能
5. 点击窗口右上角X测试配置效果
"""
            
            info_label = ttkb.Label(
                info_frame,
                text=info_text,
                justify="left",
                font=("Arial", 10),
                background="lightgray",
                relief="solid",
                borderwidth=1
            )
            info_label.pack(fill=X, padx=5, pady=5)
            
            # 状态显示
            status_label = ttkb.Label(
                root,
                text="状态：准备测试",
                font=("Arial", 12, "bold"),
                foreground="blue"
            )
            status_label.pack(pady=15)
            
            # 按钮区域
            button_frame = ttkb.Frame(root)
            button_frame.pack(pady=20)
            
            # 软件设置按钮
            settings_button = ttkb.Button(
                button_frame,
                text="软件设置",
                bootstyle="info",
                command=settings_dialog.open_software_settings_dialog
            )
            settings_button.pack(side="left", padx=10)
            
            # 最小化到托盘按钮
            minimize_button = ttkb.Button(
                button_frame,
                text="最小化到托盘",
                bootstyle="primary",
                command=lambda: (
                    tray_component.minimize_to_tray(),
                    status_label.config(text="状态：已最小化到托盘，请双击托盘图标", foreground="orange")
                )
            )
            minimize_button.pack(side="left", padx=10)
            
            # 手动恢复按钮
            restore_button = ttkb.Button(
                button_frame,
                text="手动恢复",
                bootstyle="secondary",
                command=restore_callback
            )
            restore_button.pack(side="left", padx=10)
            
            # 退出按钮
            quit_button = ttkb.Button(
                button_frame,
                text="退出程序",
                bootstyle="danger",
                command=quit_callback
            )
            quit_button.pack(side="left", padx=10)
            
            # 设置窗口关闭事件 - 根据软件设置决定行为
            def on_window_close():
                if settings_dialog.get_minimize_to_tray_setting():
                    print("[关闭] 配置为最小化到托盘")
                    if tray_component.minimize_to_tray():
                        status_label.config(text="状态：窗口已最小化到托盘", foreground="orange")
                    else:
                        print("[关闭] 最小化失败，直接退出")
                        quit_callback()
                else:
                    print("[关闭] 配置为直接关闭")
                    quit_callback()
            
            root.protocol("WM_DELETE_WINDOW", on_window_close)
            
            print("✓ 最终测试窗口已创建")
            print("✓ 请按照界面说明进行完整功能测试")
            print("✓ 观察控制台输出了解详细执行过程")
            
            # 运行测试窗口
            root.mainloop()
            
        except Exception as e:
            print(f"[错误] 测试过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
        finally:
            try:
                if 'tray_component' in locals():
                    tray_component.cleanup()
                root.destroy()
            except:
                pass


def main():
    """主测试函数"""
    print("开始最终系统托盘功能测试...")
    
    try:
        final_tray_test()
        print("\n✓ 测试完成！")
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
