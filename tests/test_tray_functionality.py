"""系统托盘功能测试脚本。

专门测试系统托盘的双击和右键菜单功能。
"""
import os
import sys
import tempfile
from pathlib import Path

# 添加src路径
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

import ttkbootstrap as ttkb
from webhook_server.config import gui_constants
from webhook_server.gui import system_tray
from webhook_server.models import gui_properties


def test_tray_functionality():
    """测试系统托盘功能"""
    print("测试系统托盘功能...")
    
    # 创建临时配置文件
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_config_path = os.path.join(temp_dir, "test_server_config.ini")
        
        # 创建基本的服务器配置文件
        with open(temp_config_path, 'w', encoding='utf-8') as f:
            f.write("""[server]
api_key=test_key
whitelist=*
message_data_table_name=test_messages
log_config_path=test_log.ini
host=127.0.0.1
port=8003
run_time=07:00-07:00
time_zone=Asia/Shanghai
expire_data_days=3
data_limit_num=100000
app_name=test_webhook_server3
enable_sql_logging=True
""")
        
        # 创建测试窗口
        root = ttkb.Window(title="系统托盘测试", size=(500, 400))
        
        try:
            # 创建GUI属性管理器
            props = gui_properties.GUIProperties(temp_config_path)
            props.root = root
            
            # 创建系统托盘组件
            tray_component = system_tray.SystemTrayComponent(props)
            
            # 检查托盘功能是否可用
            if not tray_component.is_available():
                error_label = ttkb.Label(
                    root,
                    text="系统托盘功能不可用！\n请安装 pystray 和 Pillow:\npip install pystray pillow",
                    foreground="red",
                    font=("Arial", 12)
                )
                error_label.pack(pady=50)
                
                close_button = ttkb.Button(
                    root,
                    text="关闭",
                    command=root.quit
                )
                close_button.pack(pady=20)
                
                root.mainloop()
                return
            
            # 设置托盘回调函数
            def restore_callback():
                print("恢复回调被调用")
                root.deiconify()
                root.lift()
                root.focus_force()
                root.attributes('-topmost', True)
                root.after(100, lambda: root.attributes('-topmost', False))
            
            def quit_callback():
                print("退出回调被调用")
                tray_component.cleanup()
                root.quit()
            
            tray_component.set_callbacks(
                restore_callback=restore_callback,
                quit_callback=quit_callback
            )
            
            # 创建界面元素
            title_label = ttkb.Label(
                root,
                text="系统托盘功能测试",
                font=("Arial", 16, "bold")
            )
            title_label.pack(pady=20)
            
            info_text = """
测试步骤：
1. 点击"最小化到托盘"按钮
2. 窗口应该隐藏到系统托盘
3. 双击托盘图标，窗口应该恢复显示
4. 右键托盘图标，选择"打开主界面"
5. 右键托盘图标，选择"退出程序"

注意：请确保已安装 pystray 和 Pillow
"""
            info_label = ttkb.Label(
                root,
                text=info_text,
                justify="left",
                font=("Arial", 10)
            )
            info_label.pack(pady=10)
            
            # 最小化到托盘按钮
            minimize_button = ttkb.Button(
                root,
                text="最小化到托盘",
                bootstyle="primary",
                command=lambda: tray_component.minimize_to_tray()
            )
            minimize_button.pack(pady=10)
            
            # 手动恢复按钮（用于测试）
            restore_button = ttkb.Button(
                root,
                text="手动恢复窗口",
                bootstyle="secondary",
                command=restore_callback
            )
            restore_button.pack(pady=5)
            
            # 退出按钮
            quit_button = ttkb.Button(
                root,
                text="退出程序",
                bootstyle="danger",
                command=quit_callback
            )
            quit_button.pack(pady=10)
            
            # 状态显示
            status_label = ttkb.Label(
                root,
                text="状态：程序运行中，可以测试托盘功能",
                foreground="green"
            )
            status_label.pack(pady=20)
            
            print("系统托盘测试窗口已创建")
            print("请按照界面说明进行测试...")
            
            # 运行测试窗口
            root.mainloop()
            
        except Exception as e:
            print(f"测试过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
        finally:
            try:
                if 'tray_component' in locals():
                    tray_component.cleanup()
                root.destroy()
            except:
                pass


def main():
    """主测试函数"""
    print("开始系统托盘功能测试...")
    print("=" * 50)
    
    try:
        test_tray_functionality()
        print("\n测试完成！")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
