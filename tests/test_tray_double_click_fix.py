"""测试系统托盘双击功能修复。

专门测试Windows系统上托盘双击恢复窗口的功能。
"""
import os
import sys
import time
import threading
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

import ttkbootstrap as ttkb
from webhook_server.config import gui_constants
from webhook_server.gui import system_tray
from webhook_server.models import gui_properties


def test_tray_double_click_functionality():
    """测试系统托盘双击功能"""
    print("测试系统托盘双击功能...")
    
    # 创建临时配置文件
    temp_config_path = os.path.join(
        os.path.expanduser("~"), 
        ".webhook_server", 
        "test_tray_config.ini"
    )
    
    # 确保配置目录存在
    os.makedirs(os.path.dirname(temp_config_path), exist_ok=True)
    
    # 创建基本配置文件
    with open(temp_config_path, 'w', encoding='utf-8') as f:
        f.write("""[server]
app_name = 托盘测试应用
host = 0.0.0.0
port = 8001
log_config_path = """ + gui_constants.LOG_CONFIG_PATH + """
time_zone = Asia/Shanghai
run_time = 07:00-07:00
expire_data_days = 3
data_limit_num = 100000
enable_sql_logging = true
message_data_table_name = test_message_data
api_key = test_key
whitelist = *

[client_info]
test_device = 测试设备
""")
    
    try:
        # 创建测试窗口
        root = ttkb.Window(title="托盘双击测试", size=(500, 400))
        
        try:
            # 创建GUI属性管理器
            props = gui_properties.GUIProperties(temp_config_path)
            props.root = root
            
            # 创建系统托盘组件
            tray_component = system_tray.SystemTrayComponent(props)
            
            # 检查托盘功能是否可用
            if not tray_component.is_available():
                error_label = ttkb.Label(
                    root,
                    text="系统托盘功能不可用！\n请安装 pystray 和 Pillow:\npip install pystray pillow",
                    justify="center",
                    foreground="red",
                    font=("Arial", 12)
                )
                error_label.pack(expand=True)
                
                print("系统托盘功能不可用，请安装依赖包")
                root.mainloop()
                return
            
            # 窗口恢复计数器
            restore_count = [0]
            
            # 设置托盘回调函数
            def restore_callback():
                restore_count[0] += 1
                print(f"恢复回调被调用 (第{restore_count[0]}次)")
                
                try:
                    # 检查窗口状态
                    current_state = root.state()
                    print(f"当前窗口状态: {current_state}")
                    
                    # 恢复窗口
                    if current_state == 'withdrawn':
                        root.deiconify()
                    elif current_state == 'iconic':
                        root.deiconify()
                    
                    root.state('normal')
                    root.lift()
                    root.focus_force()
                    root.attributes('-topmost', True)
                    root.after(200, lambda: root.attributes('-topmost', False))
                    
                    # 更新状态标签
                    status_label.config(
                        text=f"状态：窗口已恢复 (恢复次数: {restore_count[0]})",
                        foreground="green"
                    )
                    
                except Exception as e:
                    print(f"恢复窗口时出错: {e}")
                    status_label.config(
                        text=f"状态：恢复失败 - {e}",
                        foreground="red"
                    )
            
            def quit_callback():
                print("退出回调被调用")
                tray_component.cleanup()
                root.quit()
            
            tray_component.set_callbacks(
                restore_callback=restore_callback,
                quit_callback=quit_callback
            )
            
            # 创建界面元素
            title_label = ttkb.Label(
                root,
                text="系统托盘双击功能测试",
                justify="center",
                font=("Arial", 16, "bold")
            )
            title_label.pack(pady=20)
            
            info_text = """
测试步骤：
1. 点击"最小化到托盘"按钮
2. 窗口应该隐藏到系统托盘
3. 双击托盘图标，窗口应该恢复显示
4. 右键托盘图标，选择"显示主界面"
5. 右键托盘图标，选择"退出程序"

注意：
- 请确保已安装 pystray 和 Pillow
- 在Windows上双击托盘图标可能需要稍等片刻
- 如果双击无效，请尝试右键菜单
"""
            info_label = ttkb.Label(
                root,
                text=info_text,
                justify="left",
                font=("Arial", 10)
            )
            info_label.pack(pady=10)
            
            # 最小化到托盘按钮
            minimize_button = ttkb.Button(
                root,
                text="最小化到托盘",
                bootstyle="primary",
                command=lambda: minimize_to_tray_with_feedback()
            )
            minimize_button.pack(pady=10)
            
            def minimize_to_tray_with_feedback():
                """最小化到托盘并提供反馈"""
                print("正在最小化到托盘...")
                if tray_component.minimize_to_tray():
                    print("成功最小化到托盘")
                    status_label.config(
                        text="状态：已最小化到托盘，请双击托盘图标恢复",
                        foreground="blue"
                    )
                else:
                    print("最小化到托盘失败")
                    status_label.config(
                        text="状态：最小化到托盘失败",
                        foreground="red"
                    )
            
            # 测试恢复按钮
            restore_button = ttkb.Button(
                root,
                text="测试恢复功能",
                bootstyle="success",
                command=restore_callback
            )
            restore_button.pack(pady=5)
            
            # 状态标签
            status_label = ttkb.Label(
                root,
                text="状态：程序运行中，可以测试托盘功能",
                foreground="green"
            )
            status_label.pack(pady=20)
            
            print("系统托盘双击测试窗口已创建")
            print("请按照界面说明进行测试...")
            
            # 设置窗口关闭事件
            def on_closing():
                print("窗口关闭事件触发")
                tray_component.cleanup()
                root.destroy()
            
            root.protocol("WM_DELETE_WINDOW", on_closing)
            
            # 启动主循环
            root.mainloop()
            
        finally:
            try:
                if 'tray_component' in locals():
                    tray_component.cleanup()
                root.destroy()
            except:
                pass
    
    finally:
        # 清理临时配置文件
        try:
            if os.path.exists(temp_config_path):
                os.remove(temp_config_path)
        except:
            pass


def main():
    """主测试函数"""
    print("开始系统托盘双击功能测试...")
    print("=" * 50)
    
    try:
        test_tray_double_click_functionality()
        print("\n测试完成！")
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()