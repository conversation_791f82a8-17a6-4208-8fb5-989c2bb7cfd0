"""系统托盘组件。

提供系统托盘功能，包括最小化到托盘、托盘菜单等。
"""
import threading
import tkinter as tk
from typing import Optional, Callable

try:
    import pystray
    from pystray import MenuItem as item
    from PIL import Image
    PYSTRAY_AVAILABLE = True
except ImportError:
    PYSTRAY_AVAILABLE = False

from common.utils import logging_utils
from webhook_server.config import gui_constants
from webhook_server.models import gui_properties


class SystemTrayComponent:
    """系统托盘组件类。"""

    def __init__(self, props: gui_properties.GUIProperties):
        """初始化系统托盘组件。

        Args:
            props: GUI属性管理器实例
        """
        self.props = props
        self.tray_icon: Optional['pystray.Icon'] = None
        self.tray_thread: Optional[threading.Thread] = None
        self.is_tray_running = False
        self.restore_callback: Optional[Callable] = None
        self.quit_callback: Optional[Callable] = None
        
        # 双击检测相关属性
        self.last_click_time = 0
        self.double_click_threshold = 0.5  # 500毫秒内的第二次点击视为双击
        
        if not PYSTRAY_AVAILABLE:
            logging_utils.logger_print(
                msg="pystray not available, system tray functionality disabled", 
                custom_logger=self.props.logger
            )

    def set_callbacks(self, restore_callback: Callable, quit_callback: Callable):
        """设置回调函数

        Args:
            restore_callback: 恢复窗口的回调函数
            quit_callback: 退出程序的回调函数
        """
        self.restore_callback = restore_callback
        self.quit_callback = quit_callback

        logging_utils.logger_print(
            msg=f"tray callbacks set: restore={restore_callback is not None}, quit={quit_callback is not None}",
            custom_logger=self.props.logger
        )

    def create_tray_icon(self):
        """创建系统托盘图标"""
        if not PYSTRAY_AVAILABLE:
            logging_utils.logger_print(
                msg="cannot create tray icon: pystray not available", 
                custom_logger=self.props.logger
            )
            return False

        try:
            # 加载图标
            icon_image = Image.open(gui_constants.SMALL_ICON_PATH)

            # 创建托盘菜单 - 使用default=True使"显示主界面"成为默认操作
            # 在Windows上，这通常对应左键单击或双击（取决于系统设置）
            menu = pystray.Menu(
                item('显示主界面', self.on_restore_clicked, default=True),
                pystray.Menu.SEPARATOR,
                item('退出程序', self.on_quit_clicked)
            )

            # 创建托盘图标
            self.tray_icon = pystray.Icon(
                gui_constants.SOFTWARE_NAME,
                icon_image,
                menu=menu
            )

            # Windows托盘图标操作说明：
            # 1. 右键点击托盘图标 - 显示菜单，选择"显示主界面"
            # 2. 左键点击行为取决于系统设置和pystray实现
            # 3. 在某些Windows版本上，左键单击可能触发默认菜单项
            
            logging_utils.logger_print(
                msg="tray icon created successfully with menu-based restore functionality",
                custom_logger=self.props.logger
            )
            
            logging_utils.logger_print(
                msg="system tray icon created successfully", 
                custom_logger=self.props.logger
            )
            return True
            
        except Exception as e:
            logging_utils.logger_print(
                msg=f"failed to create tray icon: {e}", 
                custom_logger=self.props.logger
            )
            return False

    def show_tray_icon(self):
        """显示系统托盘图标"""
        if not PYSTRAY_AVAILABLE or not self.tray_icon:
            return False

        if self.is_tray_running:
            return True

        try:
            # 在单独线程中运行托盘图标
            self.tray_thread = threading.Thread(
                target=self._run_tray_icon,
                daemon=True
            )
            self.tray_thread.start()
            self.is_tray_running = True
            
            logging_utils.logger_print(
                msg="system tray icon shown", 
                custom_logger=self.props.logger
            )
            return True
            
        except Exception as e:
            logging_utils.logger_print(
                msg=f"failed to show tray icon: {e}", 
                custom_logger=self.props.logger
            )
            return False

    def _run_tray_icon(self):
        """在单独线程中运行托盘图标"""
        try:
            if self.tray_icon:
                self.tray_icon.run()
        except Exception as e:
            logging_utils.logger_print(
                msg=f"tray icon thread error: {e}", 
                custom_logger=self.props.logger
            )

    def hide_tray_icon(self):
        """隐藏系统托盘图标"""
        if not self.is_tray_running or not self.tray_icon:
            return

        try:
            self.tray_icon.stop()
            self.is_tray_running = False
            
            logging_utils.logger_print(
                msg="system tray icon hidden", 
                custom_logger=self.props.logger
            )
            
        except Exception as e:
            logging_utils.logger_print(
                msg=f"failed to hide tray icon: {e}", 
                custom_logger=self.props.logger
            )

    def minimize_to_tray(self):
        """最小化窗口到系统托盘"""
        if not PYSTRAY_AVAILABLE:
            logging_utils.logger_print(
                msg="cannot minimize to tray: pystray not available", 
                custom_logger=self.props.logger
            )
            return False

        try:
            # 创建并显示托盘图标（如果还没有）
            if not self.is_tray_running:
                if not self.create_tray_icon():
                    return False
                if not self.show_tray_icon():
                    return False

            # 隐藏主窗口
            if self.props.root:
                self.props.root.withdraw()
                
            logging_utils.logger_print(
                msg="window minimized to system tray", 
                custom_logger=self.props.logger
            )
            return True
            
        except Exception as e:
            logging_utils.logger_print(
                msg=f"failed to minimize to tray: {e}", 
                custom_logger=self.props.logger
            )
            return False

    def restore_from_tray(self):
        """从系统托盘恢复窗口"""
        try:
            if self.props.root:
                self.props.root.deiconify()
                self.props.root.lift()
                self.props.root.focus_force()
                
            logging_utils.logger_print(
                msg="window restored from system tray", 
                custom_logger=self.props.logger
            )
            
        except Exception as e:
            logging_utils.logger_print(
                msg=f"failed to restore from tray: {e}", 
                custom_logger=self.props.logger
            )

    def on_restore_clicked(self, icon=None, item=None):
        """托盘菜单"显示主界面"点击事件"""
        try:
            logging_utils.logger_print(
                msg="tray restore menu clicked",
                custom_logger=self.props.logger
            )

            if self.restore_callback:
                # 使用线程安全的方式调用回调
                def safe_restore():
                    """安全的恢复函数"""
                    try:
                        self.restore_callback()
                    except Exception as e:
                        logging_utils.logger_print(
                            msg=f"error in safe_restore from menu: {e}",
                            custom_logger=self.props.logger
                        )
                
                # 总是使用after方法调度到主线程，确保线程安全
                if self.props.root and hasattr(self.props.root, 'after'):
                    self.props.root.after(0, safe_restore)
                else:
                    # 备选方案：直接调用
                    safe_restore()
                    
        except Exception as e:
            logging_utils.logger_print(
                msg=f"error in tray restore menu: {e}",
                custom_logger=self.props.logger
            )

    def on_quit_clicked(self, icon=None, item=None):
        """托盘菜单"退出程序"点击事件"""
        try:
            logging_utils.logger_print(
                msg="tray quit clicked",
                custom_logger=self.props.logger
            )

            if self.quit_callback:
                # 使用 after 方法确保在主线程中执行
                if self.props.root and hasattr(self.props.root, 'after'):
                    self.props.root.after(0, self.quit_callback)
                else:
                    # 如果root不可用，直接调用回调
                    self.quit_callback()
        except Exception as e:
            logging_utils.logger_print(
                msg=f"error in tray quit: {e}",
                custom_logger=self.props.logger
            )

    def cleanup(self):
        """清理资源"""
        try:
            self.hide_tray_icon()
            if self.tray_thread and self.tray_thread.is_alive():
                self.tray_thread.join(timeout=1.0)
                
            logging_utils.logger_print(
                msg="system tray component cleaned up", 
                custom_logger=self.props.logger
            )
            
        except Exception as e:
            logging_utils.logger_print(
                msg=f"error during tray cleanup: {e}", 
                custom_logger=self.props.logger
            )

    def is_available(self) -> bool:
        """检查系统托盘功能是否可用
        
        Returns:
            bool: True表示可用，False表示不可用
        """
        return PYSTRAY_AVAILABLE
