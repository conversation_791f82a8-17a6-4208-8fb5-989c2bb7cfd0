"""软件设置对话框组件。

负责显示和管理软件的设置选项。
"""
import configparser
import os
from typing import Optional

import ttkbootstrap as ttkb
from ttkbootstrap.constants import *

from common.utils import ttkb_gui_utils, logging_utils, config_utils
from webhook_server.config import gui_constants
from webhook_server.models import gui_properties


class SoftwareSettingsDialogComponent:
    """软件设置对话框组件类。"""

    def __init__(self, props: gui_properties.GUIProperties):
        """初始化软件设置对话框组件。

        Args:
            props: GUI属性管理器实例
        """
        self.props = props
        self.settings_dialog: Optional[ttkb.Toplevel] = None
        self.minimize_to_tray_var: Optional[ttkb.BooleanVar] = None
        
        # 软件配置
        self.software_config = {
            'minimize_to_tray': True  # 默认最小化到托盘
        }
        
        # 加载软件配置
        self.load_software_config()

    def load_software_config(self):
        """加载软件配置文件"""
        try:
            if os.path.exists(gui_constants.SOFTWARE_CONFIG_PATH):
                config = configparser.ConfigParser(interpolation=None)
                config.optionxform = str
                config.read(gui_constants.SOFTWARE_CONFIG_PATH, encoding="utf-8")
                
                if config.has_section("software"):
                    # 读取最小化到托盘设置
                    if config.has_option("software", "minimize_to_tray"):
                        minimize_value = config.get("software", "minimize_to_tray").lower()
                        self.software_config['minimize_to_tray'] = minimize_value in ('true', '1', 'yes', 'on')
                        
                logging_utils.logger_print(
                    msg=f"software config loaded: {self.software_config}", 
                    custom_logger=self.props.logger
                )
            else:
                logging_utils.logger_print(
                    msg="software config file not found, using defaults", 
                    custom_logger=self.props.logger
                )
                # 创建默认配置文件
                self.save_software_config()
        except Exception as e:
            logging_utils.logger_print(
                msg=f"failed to load software config: {e}", 
                custom_logger=self.props.logger
            )

    def save_software_config(self):
        """保存软件配置到文件"""
        try:
            config_utils.update_config(
                gui_constants.SOFTWARE_CONFIG_PATH,
                "software",
                self.software_config
            )
            logging_utils.logger_print(
                msg=f"software config saved: {self.software_config}", 
                custom_logger=self.props.logger
            )
        except Exception as e:
            logging_utils.logger_print(
                msg=f"failed to save software config: {e}", 
                custom_logger=self.props.logger
            )

    def get_minimize_to_tray_setting(self) -> bool:
        """获取最小化到托盘设置
        
        Returns:
            bool: True表示最小化到托盘，False表示直接关闭
        """
        return self.software_config.get('minimize_to_tray', True)

    def open_software_settings_dialog(self):
        """打开软件设置对话框"""
        if getattr(self, "settings_dialog", None) and self.settings_dialog.winfo_exists():
            self.settings_dialog.lift()
            return

        # 创建对话框窗口，使用与ConfigDialogComponent相同的大小设置方式
        self.settings_dialog = ttkb.Toplevel(
            master=self.props.root,
            title="软件设置",
            size=gui_constants.SOFTWARE_CONFIG_DIALOG_SIZE,
            takefocus=True,
            topmost=True,
            resizable=(False, False),
            transient=self.props.root
        )
        ttkb_gui_utils.comm_child_win_do(self.settings_dialog, self.props.root)

        # 主框架，使用与ConfigDialogComponent相同的布局
        main_frame = ttkb.Frame(self.settings_dialog)
        main_frame.pack(fill=BOTH, expand=True, padx=20, pady=10)

        # 使用 grid 布局统一控制整体布局，与ConfigDialogComponent保持一致
        settings_frame = ttkb.LabelFrame(main_frame, text="软件设置")
        settings_frame.grid(row=0, column=0, sticky=NSEW, padx=(0,10), pady=(0,10))

        # 最小化到托盘选项，使用grid布局
        self.minimize_to_tray_var = ttkb.BooleanVar(value=self.software_config['minimize_to_tray'])

        minimize_label = ttkb.Label(settings_frame, text="关闭主界面最小化到任务栏:")
        minimize_label.grid(row=0, column=0, sticky=E, padx=5, pady=5)

        minimize_checkbutton = ttkb.Checkbutton(
            settings_frame,
            variable=self.minimize_to_tray_var,
            bootstyle="success-round-toggle",
            command=self.on_minimize_setting_changed
        )
        minimize_checkbutton.grid(row=0, column=1, sticky=W, padx=(5,90), pady=5)

        # 保存按钮框架，与ConfigDialogComponent保持一致
        save_frame = ttkb.Frame(main_frame)
        save_frame.grid(row=1, column=0, sticky=E, pady=(10, 0))

        ttkb.Button(save_frame, text="保存软件设置", command=self.close_settings_dialog).pack(anchor=E, padx=10)

        # 关闭事件
        self.settings_dialog.protocol("WM_DELETE_WINDOW", self.cancel_settings_dialog)

    def on_minimize_setting_changed(self):
        """最小化设置改变时的回调"""
        if self.minimize_to_tray_var:
            self.software_config['minimize_to_tray'] = self.minimize_to_tray_var.get()
            self.save_software_config()

    def close_settings_dialog(self):
        """关闭设置对话框并保存设置"""
        if self.minimize_to_tray_var:
            self.software_config['minimize_to_tray'] = self.minimize_to_tray_var.get()
            self.save_software_config()
        
        if self.settings_dialog:
            self.settings_dialog.destroy()
            self.settings_dialog = None

    def cancel_settings_dialog(self):
        """取消设置对话框，不保存更改"""
        if self.settings_dialog:
            self.settings_dialog.destroy()
            self.settings_dialog = None
