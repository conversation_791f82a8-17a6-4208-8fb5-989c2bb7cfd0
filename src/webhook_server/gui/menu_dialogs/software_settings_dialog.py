"""软件设置对话框组件。

负责显示和管理软件的设置选项。
"""
import configparser
import os
from typing import Optional

import ttkbootstrap as ttkb
from ttkbootstrap.constants import *

from common.utils import ttkb_gui_utils, logging_utils, config_utils
from webhook_server.config import gui_constants
from webhook_server.models import gui_properties


class SoftwareSettingsDialogComponent:
    """软件设置对话框组件类。"""

    def __init__(self, props: gui_properties.GUIProperties):
        """初始化软件设置对话框组件。

        Args:
            props: GUI属性管理器实例
        """
        self.props = props
        self.settings_dialog: Optional[ttkb.Toplevel] = None
        self.minimize_to_tray_var: Optional[ttkb.BooleanVar] = None
        
        # 软件配置
        self.software_config = {
            'minimize_to_tray': True  # 默认最小化到托盘
        }
        
        # 加载软件配置
        self.load_software_config()

    def load_software_config(self):
        """加载软件配置文件"""
        try:
            if os.path.exists(gui_constants.SOFTWARE_CONFIG_PATH):
                config = configparser.ConfigParser(interpolation=None)
                config.optionxform = str
                config.read(gui_constants.SOFTWARE_CONFIG_PATH, encoding="utf-8")
                
                if config.has_section("software"):
                    # 读取最小化到托盘设置
                    if config.has_option("software", "minimize_to_tray"):
                        minimize_value = config.get("software", "minimize_to_tray").lower()
                        self.software_config['minimize_to_tray'] = minimize_value in ('true', '1', 'yes', 'on')
                        
                logging_utils.logger_print(
                    msg=f"software config loaded: {self.software_config}", 
                    custom_logger=self.props.logger
                )
            else:
                logging_utils.logger_print(
                    msg="software config file not found, using defaults", 
                    custom_logger=self.props.logger
                )
                # 创建默认配置文件
                self.save_software_config()
        except Exception as e:
            logging_utils.logger_print(
                msg=f"failed to load software config: {e}", 
                custom_logger=self.props.logger
            )

    def save_software_config(self):
        """保存软件配置到文件"""
        try:
            config_utils.update_config(
                gui_constants.SOFTWARE_CONFIG_PATH,
                "software",
                self.software_config
            )
            logging_utils.logger_print(
                msg=f"software config saved: {self.software_config}", 
                custom_logger=self.props.logger
            )
        except Exception as e:
            logging_utils.logger_print(
                msg=f"failed to save software config: {e}", 
                custom_logger=self.props.logger
            )

    def get_minimize_to_tray_setting(self) -> bool:
        """获取最小化到托盘设置
        
        Returns:
            bool: True表示最小化到托盘，False表示直接关闭
        """
        return self.software_config.get('minimize_to_tray', True)

    def open_software_settings_dialog(self):
        """打开软件设置对话框"""
        if getattr(self, "settings_dialog", None) and self.settings_dialog.winfo_exists():
            self.settings_dialog.lift()
            return
            
        # 创建对话框窗口
        self.settings_dialog = ttkb.Toplevel(
            master=self.props.root,
            title="软件设置",
            size=(400, 200),
            takefocus=True,
            topmost=True,
            resizable=(False, False),
            transient=self.props.root
        )
        ttkb_gui_utils.comm_child_win_do(self.settings_dialog, self.props.root)
        
        # 主框架
        main_frame = ttkb.Frame(self.settings_dialog)
        main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ttkb.Label(
            main_frame,
            text="软件设置",
            font=(self.props.ui_font_family, 14, "bold")
        )
        title_label.pack(pady=(0, 20))
        
        # 设置选项框架
        options_frame = ttkb.Frame(main_frame)
        options_frame.pack(fill=X, pady=10)
        
        # 最小化到托盘选项
        self.minimize_to_tray_var = ttkb.BooleanVar(value=self.software_config['minimize_to_tray'])
        
        minimize_frame = ttkb.Frame(options_frame)
        minimize_frame.pack(fill=X, pady=5)
        
        minimize_label = ttkb.Label(
            minimize_frame,
            text="点击右上角 X 时:",
            font=(self.props.ui_font_family, 10)
        )
        minimize_label.pack(side=LEFT)
        
        minimize_checkbutton = ttkb.Checkbutton(
            minimize_frame,
            text="最小化到任务栏",
            variable=self.minimize_to_tray_var,
            bootstyle="success-round-toggle",
            command=self.on_minimize_setting_changed
        )
        minimize_checkbutton.pack(side=RIGHT)
        
        # 说明文字
        info_label = ttkb.Label(
            main_frame,
            text="勾选：点击 X 最小化到任务栏\n不勾选：点击 X 直接关闭程序",
            font=(self.props.ui_font_family, 9),
            foreground="gray"
        )
        info_label.pack(pady=(10, 0))
        
        # 按钮框架
        button_frame = ttkb.Frame(main_frame)
        button_frame.pack(fill=X, pady=(20, 0))
        
        # 确定按钮
        ok_button = ttkb.Button(
            button_frame,
            text="确定",
            bootstyle="primary",
            command=self.close_settings_dialog
        )
        ok_button.pack(side=RIGHT, padx=(5, 0))
        
        # 取消按钮
        cancel_button = ttkb.Button(
            button_frame,
            text="取消",
            bootstyle="secondary",
            command=self.cancel_settings_dialog
        )
        cancel_button.pack(side=RIGHT)

    def on_minimize_setting_changed(self):
        """最小化设置改变时的回调"""
        if self.minimize_to_tray_var:
            self.software_config['minimize_to_tray'] = self.minimize_to_tray_var.get()
            self.save_software_config()

    def close_settings_dialog(self):
        """关闭设置对话框并保存设置"""
        if self.minimize_to_tray_var:
            self.software_config['minimize_to_tray'] = self.minimize_to_tray_var.get()
            self.save_software_config()
        
        if self.settings_dialog:
            self.settings_dialog.destroy()
            self.settings_dialog = None

    def cancel_settings_dialog(self):
        """取消设置对话框，不保存更改"""
        # 恢复原始设置
        self.load_software_config()
        
        if self.settings_dialog:
            self.settings_dialog.destroy()
            self.settings_dialog = None
