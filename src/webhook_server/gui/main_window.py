"""Webhook服务器GUI主窗口控制器。

此模块提供了Webhook服务器的主窗口控制器，负责协调各个组件。
"""
import configparser
import copy
import logging
import threading
import time
from tkinter import messagebox
from typing import Optional
from zoneinfo import ZoneInfo

import ttkbootstrap as ttkb
from ttkbootstrap.constants import *

from common.models import single_instance_meta
from common.utils import logging_utils, config_utils, common_utils, self_log, gui_utils, ttkb_gui_utils
from webhook_server.config import gui_constants, constants
from webhook_server.gui import data_table, status_monitor, server_control
from webhook_server.gui import system_tray
from webhook_server.gui.menu_dialogs import config_dialog, client_info_dialog, about_dialog, network_fix_dialog, \
    software_settings_dialog
from webhook_server.gui.other_main_gui import notification_bar, usage_notes
# GUI组件导入
from webhook_server.models import gui_properties


class WebhookServerGUI(metaclass=single_instance_meta.SingleInstanceMeta):
    """Webhook服务器GUI主界面类。

    提供Webhook服务器的图形用户界面，包括服务器控制、数据显示等功能。
    传入的config_path为服务端配置文件路径，其必须有效。
    """

    def __init__(self, server_config_path: str) -> None:
        """初始化Webhook服务器GUI。

        Args:
            server_config_path: 服务端配置文件路径
        """
        logging_utils.logger_print(msg="initializing webhook server gui", custom_logger=None)

        # 各种组件
        self.config_dialog_co:Optional[config_dialog.ConfigDialogComponent] = None
        self.client_info_dialog_co:Optional[client_info_dialog.ClientInfoDialogComponent] = None
        self.about_dialog_co:Optional[about_dialog.AboutDialogComponent] = None
        self.network_fix_dialog_co:Optional[network_fix_dialog.NetworkFixDialogComponent] = None
        self.software_settings_dialog_co:Optional[software_settings_dialog.SoftwareSettingsDialogComponent] = None

        self.notification_bar_co:Optional[notification_bar.NotificationBarComponent] = None
        self.data_table_co:Optional[data_table.DataTableComponent] = None
        self.status_monitor_co:Optional[status_monitor.StatusMonitorComponent] = None
        self.usage_notes_co:Optional[usage_notes.UsageNotesComponent] = None
        self.server_control_co:Optional[server_control.ServerControlComponent]=None

        # 系统托盘组件
        self.system_tray_co:Optional[system_tray.SystemTrayComponent] = None
        
        # 防重复恢复的标志
        self._restoring_from_tray = False

        # 初始化GUI属性管理器
        self.props = gui_properties.GUIProperties(server_config_path)
        # 主界面 设置主窗口大小固定不可调整
        self.root = ttkb.Window(themename=gui_constants.DEFAULT_THEME,title=gui_constants.SOFTWARE_NAME,size=gui_constants.WEBHOOK_SERVER_GUI_SIZE,resizable=(False, False),alpha=0.98,iconphoto=gui_constants.SMALL_ICON_PATH)
        self.props.root=self.root
        self.initialize_components_with_error_handling()

    def initialize_components_with_error_handling(self):
        """
        在创建主窗口之后,后续需要在初始化过程中需要做的事情:
        其必须在try/except中调用块中调用,一旦出现异常直接就可以关闭主窗口
        """
        try:
            ttkb_gui_utils.set_available_unified_font_families(self.props.root)
            self.props.ui_font_family= ttkb_gui_utils.first_available_font_family
            # 控件的初始化必须在root创建之后
            self.props.server_total_uptime_var = ttkb.StringVar(value="服务端运行总时长:0天 00:00:00")
            self.props.server_pid_var=ttkb.StringVar(value=gui_constants.SERVER_PID_DEFAULT_VAR)
            self.props.server_memory_var=ttkb.StringVar(value=gui_constants.SERVER_MEMORY_DEFAULT_VAR)

            # 读取配置文件并加载到当前实例属性中
            self.load_config()
            logging_utils.logger_print(msg="configuration loaded successfully", custom_logger=self.props.logger)

            # 创建界面
            self.create_widgets()
            logging_utils.logger_print(msg="gui widgets created successfully", custom_logger=self.props.logger)
            gui_utils.main_gui_after_create_widgets_comm_do(self.props.root)

            # 初始化系统托盘
            self.system_tray_co = system_tray.SystemTrayComponent(self.props)
            if self.system_tray_co.is_available():
                self.system_tray_co.set_callbacks(
                    restore_callback=self.restore_from_tray,
                    quit_callback=self.quit_application
                )
                logging_utils.logger_print(
                    msg="system tray component initialized successfully",
                    custom_logger=self.props.logger
                )
            else:
                logging_utils.logger_print(
                    msg="system tray not available - pystray/pillow not installed",
                    custom_logger=self.props.logger
                )

            # 监听关闭事件
            self.props.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            logging_utils.logger_print(msg="webhook server gui initialization completed", custom_logger=self.props.logger)

            threading.Thread(target=self.show_initial_table_tooltip, name="initial_table_tooltip_thread", daemon=True).start()
        except BaseException:
            logging_utils.logger_print(msg=f"初始化 webhook server gui出现异常", custom_logger=self.props.logger, use_exception=True)
            if hasattr(self.props, 'server_monitor') and self.props.server_monitor is not None:
                self.props.server_monitor.stop()
            if hasattr(self.props,'config_manager') and self.props.config_manager is not None:
                self.props.config_manager.unregister_config()
            if hasattr(self.props, 'root') and self.props.root is not None:
                self.props.root.destroy()
                self.props.root=None
            raise


    def create_widgets(self):
        """创建GUI组件。"""
        # 主界面 顶部滚动通知栏
        self.notification_bar_co=notification_bar.NotificationBarComponent(self.props)
        self.notification_bar_co.create_widgets()
        # 创建顶部菜单栏
        menu_bar = ttkb.Menu(self.props.root)
        self.props.root.config(menu=menu_bar)

        # 创建"设置"菜单
        settings_menu = ttkb.Menu(menu_bar, tearoff=0)
        menu_bar.add_cascade(label="设置", menu=settings_menu)
        # 创建"设置"菜单下的"服务端配置"选项
        self.config_dialog_co = config_dialog.ConfigDialogComponent(self.props)
        settings_menu.add_command(label="服务端配置", command=self.config_dialog_co.open_config_dialog)
        # 创建"设置"菜单下的"发信设备标识信息"选项
        self.client_info_dialog_co = client_info_dialog.ClientInfoDialogComponent(self.props)
        settings_menu.add_command(label="发信设备标识信息", command=self.client_info_dialog_co.open_client_info_dialog)
        # 创建"设置"菜单下的"软件设置"选项
        self.software_settings_dialog_co = software_settings_dialog.SoftwareSettingsDialogComponent(self.props)
        settings_menu.add_command(label="软件设置", command=self.software_settings_dialog_co.open_software_settings_dialog)
        # 创建"设置"菜单下的"关于"选项
        self.about_dialog_co = about_dialog.AboutDialogComponent(self.props)
        settings_menu.add_command(label="关于", command=self.about_dialog_co.open_about_dialog)

        # 创建"问题修复"菜单
        fix_menu = ttkb.Menu(menu_bar, tearoff=0)
        menu_bar.add_cascade(label="问题修复", menu=fix_menu)
        # 创建"问题修复"菜单下的"网络通信修复"选项
        self.network_fix_dialog_co = network_fix_dialog.NetworkFixDialogComponent(self.props)
        fix_menu.add_command(label="网络通信修复", command=self.network_fix_dialog_co.open_fix_network_dialog)

        # 主界面中的主内容区域（使用Frame作为容器）--- 【数据显示区域、服务端运行状态区域】
        content_frame = ttkb.Frame(self.props.root)
        content_frame.pack(fill=BOTH, expand=True, padx=20, pady=(5,0))

        # 主界面中的数据显示区域
        self.data_table_co = data_table.DataTableComponent(self.props, content_frame)
        self.data_table_co.create_widgets()
        # 主界面中的服务端运行状态区域
        self.status_monitor_co = status_monitor.StatusMonitorComponent(self.props, content_frame)
        self.status_monitor_co.create_widgets()
        # 主界面中的程序使用说明区域
        self.usage_notes_co = usage_notes.UsageNotesComponent(self.props, content_frame)
        self.usage_notes_co.create_widgets()
        # 主界面中的最下面的按钮区域
        self.server_control_co = server_control.ServerControlComponent(self.props, self.config_dialog_co, self.status_monitor_co, self.data_table_co)
        self.server_control_co.create_widgets()


    def load_config(self):
        try:
            server_config = configparser.ConfigParser(interpolation=None)
            # key保持原本的大小写
            server_config.optionxform = str
            server_config.read(self.props.config_file_path, encoding="utf-8")
            # 加载服务端配置:其中存在用户界面自定义配置项，这时对应的配置项就不需要检验
            self.props.server_config_values = config_utils.section_to_dict(server_config, "server", True)
            # 加载发信设备标识信息
            if "client_info" in server_config:
                self.props.client_info_entries = config_utils.section_to_dict(server_config, "client_info", allow_empty_section=True)
                self.props.file_client_info_entries=copy.deepcopy(self.props.client_info_entries)
        except Exception as server_config_ex:
            self.props.show_operation_error(error=server_config_ex, error_msg="加载服务端配置失败",parent_gui=self.props.root,no_exit=False)

        # 用户界面自定义配置项缺失，提示用户必须填写所有必填字段
        miss_user_custom_keys = common_utils.get_miss_key_in_dict(dict_obj=self.props.server_config_values, required_keys=gui_constants.USER_CUSTOM_KEYS)
        if not self.props.client_info_entries:
            miss_user_custom_keys.add("client_info")
        if miss_user_custom_keys:
            key_desc_set = {constants.SERVER_KEY_DESC[user_custom_key] for user_custom_key in miss_user_custom_keys}
            msg=f"服务端配置项缺失: 【{', '.join(key_desc_set)}】, 请填写之后保存配置然后运行服务端!"
            self.props.notification_bar_text=msg+"   "+self.props.notification_bar_text

        self.props.log_config_path = self.props.server_config_values["log_config_path"]
        self.props.config_file_time_zone=ZoneInfo(self.props.server_config_values["time_zone"])
        # gui和服务端日志文件统一
        self_log.setup_logging(self.props.log_config_path, time_zone=self.props.config_file_time_zone, filename_prefix=self.props.server_config_values["app_name"])
        self.props.logger = logging.getLogger(__name__)

    def on_closing(self):
        """窗口关闭事件处理"""
        # 检查软件设置，决定是最小化到托盘还是直接关闭
        if (self.software_settings_dialog_co and
            self.software_settings_dialog_co.get_minimize_to_tray_setting() and
            self.system_tray_co and
            self.system_tray_co.is_available()):
            # 最小化到托盘
            if self.system_tray_co.minimize_to_tray():
                return
            else:
                # 如果托盘功能失败，继续执行关闭逻辑
                logging_utils.logger_print(
                    msg="failed to minimize to tray, proceeding with normal close",
                    custom_logger=self.props.logger
                )

        # 直接关闭程序的逻辑
        self.quit_application()

    def quit_application(self):
        """退出应用程序"""
        # 考虑到配置在加载时出现异常和配置时出现异常的情况,这个时候保存配置就会使得原本在配置文件中的配置丢失,所以这里不保存配置
        # 关闭前检查服务端是否运行
        if self.props.server_process_alive():
            if messagebox.askokcancel("退出", "服务端正在运行，确定要退出吗？",parent=self.props.root):
                self.server_control_co.stop_server()
            else:
                # 取消退出
                return

        # 确保停止所有监控器
        self.status_monitor_co.stop_server_process_monitor()

        # 清理系统托盘
        if self.system_tray_co:
            self.system_tray_co.cleanup()

        gui_utils.gui_close(self.props.root)
        self.props.root=None

    def restore_from_tray(self):
        """从系统托盘恢复窗口"""
        # 防止重复调用
        if self._restoring_from_tray:
            logging_utils.logger_print(
                msg="restore already in progress, ignoring duplicate call",
                custom_logger=self.props.logger
            )
            return
            
        self._restoring_from_tray = True
        
        try:
            logging_utils.logger_print(
                msg="restoring window from tray",
                custom_logger=self.props.logger
            )

            if not self.props.root:
                logging_utils.logger_print(
                    msg="cannot restore: root window is None",
                    custom_logger=self.props.logger
                )
                return

            # 检查窗口是否已经可见
            try:
                current_state = self.props.root.state()
                logging_utils.logger_print(
                    msg=f"current window state: {current_state}",
                    custom_logger=self.props.logger
                )


                # 恢复窗口显示
                if current_state in ('withdrawn', 'iconic'):
                    self.props.root.deiconify()
                
                # 确保窗口正常显示
                self.props.root.state('normal')
                
                # 将窗口置于最前面
                self.props.root.lift()
                
                # 强制获取焦点
                self.props.root.focus_force()
                
                # 临时置顶确保可见，然后取消置顶
                self.props.root.attributes('-topmost', True)
                self.props.root.update()  # 强制更新
                self.props.root.after(200, lambda: self.props.root.attributes('-topmost', False))

                logging_utils.logger_print(
                    msg="window restored from tray successfully",
                    custom_logger=self.props.logger
                )
                
            except Exception as restore_error:
                logging_utils.logger_print(
                    msg=f"error during window restore: {restore_error}",
                    custom_logger=self.props.logger
                )
                # 备选恢复方法
                try:
                    self.props.root.deiconify()
                    self.props.root.lift()
                except:
                    pass

        except Exception as e:
            logging_utils.logger_print(
                msg=f"error restoring from tray: {e}",
                custom_logger=self.props.logger
            )
        finally:
            # 重置恢复标志
            self._restoring_from_tray = False

    def show_initial_table_tooltip(self):
        """
        显示初始表格提示信息,必须在主界面出现之后执行该方法
        """
        time.sleep(0.5)
        self.data_table_co.data_ui_no_show(show_msg="服务端未启动不显示数据")


