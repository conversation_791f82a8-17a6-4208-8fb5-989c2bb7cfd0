# 系统托盘功能使用说明

## 功能概述

本次更新完善了系统托盘功能，用户可以通过托盘图标来控制主界面窗口的显示和隐藏。

## 主要改进

### 1. 双击事件处理优化
- 改进了Windows系统上的双击事件处理逻辑
- 增加了线程安全的回调机制
- 优化了窗口恢复的可靠性

### 2. 窗口恢复逻辑增强
- 添加了窗口状态检测
- 改进了窗口置顶和焦点获取
- 增加了错误处理和日志记录

### 3. 托盘菜单改进
- 将"显示主界面"设为默认菜单项
- 优化了菜单点击的线程安全处理

## 使用方法

### 基本操作流程

1. **启动程序**
   - 正常启动主程序
   - 确保已安装必要依赖：`pystray` 和 `pillow`

2. **设置最小化到托盘**
   - 打开"设置" → "软件设置"
   - 勾选"点击右上角 X 时：最小化到任务栏"
   - 点击"确定"保存设置

3. **最小化到托盘**
   - 点击窗口右上角的 X 按钮
   - 窗口将隐藏到系统托盘

4. **恢复窗口显示**
   - **主要方法**：右键托盘图标，选择"显示主界面"
   - **备选方法**：左键点击托盘图标（在某些Windows版本上可能有效）

5. **退出程序**
   - 右键托盘图标，选择"退出程序"

## 技术细节

### 依赖要求
```
pystray>=0.19.0
pillow>=10.2.0
```

### 支持的操作系统
- Windows 10/11
- 其他系统理论上也支持，但主要针对Windows优化

### 关键改进点

1. **线程安全处理**
   ```python
   # 使用after方法确保在主线程中执行
   if self.props.root and hasattr(self.props.root, 'after'):
       self.props.root.after(0, safe_restore)
   ```

2. **窗口状态检测**
   ```python
   current_state = self.props.root.state()
   if current_state == 'withdrawn':
       self.props.root.deiconify()
   elif current_state == 'iconic':
       self.props.root.deiconify()
   ```

3. **强制窗口显示**
   ```python
   self.props.root.state('normal')
   self.props.root.lift()
   self.props.root.focus_force()
   self.props.root.attributes('-topmost', True)
   self.props.root.after(200, lambda: self.props.root.attributes('-topmost', False))
   ```

## 测试方法

### 使用测试脚本
项目提供了两个测试脚本来验证托盘功能：

1. **完整功能测试**
   ```bash
   python tests/test_tray_double_click_fix.py
   ```

2. **简化测试**
   ```bash
   python tests/simple_tray_double_click_test.py
   ```

### 手动测试步骤

1. 启动主程序
2. 在软件设置中启用"最小化到托盘"
3. 点击窗口右上角 X 按钮
4. 观察托盘区域是否出现程序图标
5. 右键托盘图标，选择"显示主界面"，检查窗口是否恢复
6. 测试左键点击托盘图标（可能在某些系统上有效）

## 故障排除

### 常见问题

1. **点击托盘图标无反应**
   - 检查是否安装了 pystray 和 pillow
   - 使用右键菜单的"显示主界面"选项（推荐方法）
   - 查看日志文件中的错误信息

2. **托盘图标不显示**
   - 确认依赖包已正确安装
   - 检查系统托盘设置是否允许显示图标
   - 查看程序日志中的错误信息

3. **窗口恢复位置不正确**
   - 这是正常现象，窗口会恢复到默认位置
   - 可以手动调整窗口位置

### 日志信息

程序会记录托盘相关的操作日志，包括：
- 托盘图标创建和显示
- 双击和菜单点击事件
- 窗口恢复操作
- 错误信息

日志文件位置：`~/.webhook_server/logs/`

## 配置文件

软件设置保存在：`~/.webhook_server/software_config.ini`

```ini
[software]
minimize_to_tray = true
```

## 注意事项

1. **首次使用**：首次启用托盘功能时，系统可能会询问是否允许程序在托盘中显示图标
2. **性能影响**：托盘功能对系统性能影响很小
3. **兼容性**：主要针对Windows系统优化，其他系统可能需要额外配置
4. **依赖管理**：确保 pystray 和 pillow 版本符合要求

## 更新历史

- **v1.0.1**: 完善了托盘双击功能，改进了Windows兼容性
- 增加了线程安全处理
- 优化了窗口恢复逻辑
- 添加了详细的日志记录